import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'activate_subscription_page.dart';

class PremiumSubscriptionPage extends StatelessWidget {
  // إضافة خاصية وظيفة الرجوع
  final VoidCallback? onReturn;

  const PremiumSubscriptionPage({
    Key? key,
    this.onReturn,
  }) : super(key: key);

  Future<void> _launchTelegram() async {
    final Uri url = Uri.parse('https://t.me/t657k');
    try {
      if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
        throw Exception('Could not launch $url');
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF1565C0),
                Color(0xFF0D47A1),
              ],
            ),
          ),
          child: Safe<PERSON><PERSON>(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Align(
                    alignment: Alignment.topLeft,
                    child: Container(
                      decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: Icon(Icons.arrow_forward, color: Colors.white),
                        onPressed: () {
                          // إذا كانت هناك وظيفة رجوع محددة، استخدمها
                          if (onReturn != null) {
                            onReturn!();
                          } else {
                            // خلاف ذلك، استخدم السلوك الافتراضي للرجوع
                            Navigator.pop(context);
                          }
                        },
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    physics: BouncingScrollPhysics(),
                    child: Padding(
                      padding: const EdgeInsets.all(22.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          _buildHeader(),
                          SizedBox(height: 20),
                          _buildSubscriptionCard(context),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          'الاشتراك المميز',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 24, // تصغير حجم العنوان
            fontWeight: FontWeight.bold,
            color: Colors.white,
            shadows: [
              Shadow(
                offset: Offset(0, 2),
                blurRadius: 4,
                color: Colors.black.withOpacity(0.3),
              ),
            ],
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 4), // تقليل المسافة
        Text(
          'احصل على تجربة تعليمية متكاملة',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 14, // تصغير حجم النص الفرعي
            color: Colors.white.withOpacity(0.9),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSubscriptionCard(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16), // تقليل التباعد الداخلي
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 30,
            offset: Offset(0, 15),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildPriceSection(),
          Divider(height: 24, color: Colors.grey.withOpacity(0.2)), // تقليل ارتفاع الفاصل
          _buildFeaturesList(),
          SizedBox(height: 24),
          _buildActionButtons(context),
          SizedBox(height: 16),
          _buildSupportText(),
        ],
      ),
    );
  }

  Widget _buildPriceSection() {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Color(0xFF1565C0).withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            'اشتراك مدى الحياة',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 18, // تصغير حجم النص
              fontWeight: FontWeight.bold,
              color: Color(0xFF1565C0),
            ),
          ),
        ),
        SizedBox(height: 12),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF1565C0), Color(0xFF0D47A1)],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Color(0xFF1565C0).withOpacity(0.3),
                blurRadius: 12,
                offset: Offset(0, 6),
              ),
            ],
          ),
          child: Text(
            '5,000 دينار عراقي فقط',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 16, // تصغير حجم النص
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        SizedBox(height: 8),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Color(0xFF4CAF50).withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            'عرض لفترة محدودة',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 14, // تصغير حجم النص
              fontWeight: FontWeight.bold,
              color: Color(0xFF4CAF50),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturesList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFeatureItem('محاولات غير محدودة لكل الامتحانات', Icons.all_inclusive),
        _buildFeatureItem('وصول حصري إلى الملازم والمواد المهمة', Icons.library_books),
        _buildFeatureItem('تحديثات مستمرة للاسئلة المكررة في الامتحان الرسمي', Icons.update),
        _buildFeatureItem('نصائح وإرشادات لمساعدتك في النجاح', Icons.tips_and_updates),
        _buildFeatureItem('دعم فني مخصص على مدار الساعة', Icons.support_agent),
      ],
    );
  }

  Widget _buildFeatureItem(String text, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0), // تقليل المسافة بين العناصر
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Color(0xFF1565C0).withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: Color(0xFF1565C0), size: 16), // تصغير حجم الأيقونة
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 14, // تصغير حجم النص
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // زر الاشتراك الرئيسي
        Container(
          height: 56,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF1565C0), Color(0xFF0D47A1)],
              begin: Alignment.centerRight,
              end: Alignment.centerLeft,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Color(0xFF1565C0).withValues(alpha: 0.4),
                blurRadius: 15,
                offset: Offset(0, 8),
              ),
            ],
          ),
          child: ElevatedButton(
            onPressed: _launchTelegram,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.star,
                  color: Colors.white,
                  size: 24,
                ),
                SizedBox(width: 12),
                Text(
                  'اشترك في الباقة المميزة',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 16),

        // فاصل مع نص "أو"
        Row(
          children: [
            Expanded(
              child: Container(
                height: 1,
                color: Colors.grey.withValues(alpha: 0.3),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'أو',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Expanded(
              child: Container(
                height: 1,
                color: Colors.grey.withValues(alpha: 0.3),
              ),
            ),
          ],
        ),

        SizedBox(height: 16),

        // زر كود التفعيل
        Container(
          height: 52,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Color(0xFF1565C0),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Color(0xFF1565C0).withValues(alpha: 0.1),
                blurRadius: 10,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: ElevatedButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => ActivateSubscriptionPage()),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.vpn_key_outlined,
                  color: Color(0xFF1565C0),
                  size: 22,
                ),
                SizedBox(width: 12),
                Text(
                  'لديك كود تفعيل؟',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1565C0),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }



  Widget _buildSupportText() {
    return Text(
      'سيتم تحويلك إلى تيليجرام للتواصل مع الدعم الفني',
      style: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 12, // تصغير حجم النص
        color: Colors.grey[600],
      ),
      textAlign: TextAlign.center,
    );
  }
}