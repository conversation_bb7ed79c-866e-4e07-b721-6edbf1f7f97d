import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intp_iraq/add_questions_page.dart';
import 'package:intp_iraq/services/question_service.dart';
import 'package:intp_iraq/services/subscription_service.dart';
import 'english_exam_page.dart';
import 'arabic_exam_page.dart';
import 'computer_exam_page.dart';
import 'official_links_page.dart';
import 'login_page.dart';
import 'settings_page.dart';
import 'contact_us_page.dart';
import 'about_app_page.dart';
import 'important_materials_page.dart';
import 'tips_guidance_page.dart';
import 'premium_subscription_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String _username = '';
  bool _mounted = true;
  bool _isAdmin = false;
  bool _isPremium = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _checkPremiumStatus();
  }

  @override
  void dispose() {
    _mounted = false;
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        DocumentSnapshot userData = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();
            
        Map<String, dynamic>? data = userData.data() as Map<String, dynamic>?;
        
        if (_mounted && data != null) {
          setState(() {
            _username = data['username'] ?? '';
            _isAdmin = user.uid == 'VTmLpE1WBjPHZF7UXezhKXeSLA83';
          });
        }
      }
    } catch (e) {
      print('Error loading user data: $e');
    }
  }

  Future<void> _checkPremiumStatus() async {
    bool isPremium = await SubscriptionService.isPremiumUser();
    if (_mounted) {
      setState(() {
        _isPremium = isPremium;
      });
    }
  }

  Future<void> _updateQuestions() async {
    try {
      // إظهار مؤشر التحميل المحسن
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            backgroundColor: Colors.white,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                  SizedBox(height: 20),
                  Text(
                    'جاري تحديث الأسئلة...',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    'يرجى الانتظار',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );

      await QuestionService.updateQuestions();
      
      if (!mounted) return;
      Navigator.pop(context); // إغلاق مؤشر التحميل
      
      // رسالة نجاح محسنة
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 10),
              Text(
                'تم تحديث الأسئلة بنجاح',
                style: TextStyle(fontFamily: 'Cairo'),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );
    } catch (e) {
      print('Error in _updateQuestions: $e');
      if (!mounted) return;
      Navigator.pop(context); // إغلاق مؤشر التحميل
      
      // رسالة خطأ محسنة
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.error_outline, color: Colors.white),
              SizedBox(width: 10),
              Text(
                'حدث خطأ أثناء تحديث الأسئلة',
                style: TextStyle(fontFamily: 'Cairo'),
              ),
            ],
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSize _buildAppBar() {
    return PreferredSize(
      preferredSize: Size.fromHeight(100),
      child: AppBar(
        title: Text(
          'الصفحة الرئيسية',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 28,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.transparent,
        centerTitle: true,
        leadingWidth: 40,
        automaticallyImplyLeading: false,
        elevation: 0,
        titleSpacing: 0, // Changed from NavigationToolbar.kMiddleSpacing to 0
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFF1565C0), Color(0xFF0D47A1)],
            ),
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: CupertinoButton(
              onPressed: _showActionSheet,
              child: Icon(CupertinoIcons.ellipsis_vertical, color: Colors.white, size: 28),
            ),
          ),
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(30),
          child: Container(
            height: 30,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30),
                topRight: Radius.circular(30),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: SafeArea(
        child: Column(
          children: [
            _buildWelcomeMessage(),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16.0, 20.0, 16.0, 16.0),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                  ),
                  child: GridView.count(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1.4,
                    children: [
                      _buildExamCard('امتحان اللغة الإنكليزية', CupertinoIcons.book, EnglishExamPage()),
                      _buildExamCard('امتحان اللغة العربية', CupertinoIcons.textbox, ArabicExamPage()),
                      _buildExamCard('الملازم والمواد المهمة', CupertinoIcons.doc_text_search, ImportantMaterialsPage()),
                      _buildExamCard('امتحان الحاسوب', CupertinoIcons.desktopcomputer, ComputerExamPage()),
                      _buildUpdateQuestionsCard(),
                      _buildExamCard('نصائح وإرشادات', CupertinoIcons.lightbulb_fill, TipsGuidancePage()),
                      _buildExamCard('الروابط الرسمية للامتحانات', CupertinoIcons.link, OfficialLinksPage()),
                      if (_isAdmin) _buildExamCard('إضافة الأسئلة', CupertinoIcons.add_circled, AddQuestionsPage()),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: 30),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeMessage() {
    return Container(
      margin: EdgeInsets.fromLTRB(20, 20, 20, 5),
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 15),
      decoration: BoxDecoration(
        color: Color(0xFFE3F2FD),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'مرحباً بك $_username',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1565C0),
            ),
          ),
          SizedBox(height: 4),
          Text(
            'اختر الامتحان الذي تريد البدء به',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 16,
              color: Color(0xFF1976D2),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showActionSheet() {
    showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        title: Text('مرحباً يا $_username', style: TextStyle(fontFamily: 'Cairo')),
        actions: <Widget>[
          _buildActionSheetItem('الإعدادات', () => Navigator.push(context, CupertinoPageRoute(builder: (context) => SettingsPage()))),
          _buildActionSheetItem('تواصل معنا', () => Navigator.push(context, CupertinoPageRoute(builder: (context) => ContactUsPage()))),
          _buildActionSheetItem('حول التطبيق', () => Navigator.push(context, CupertinoPageRoute(builder: (context) => AboutAppPage()))),
          _buildActionSheetItem('تسجيل الخروج', () => _handleLogout(context)),
        ],
        cancelButton: CupertinoActionSheetAction(
          child: Text('إلغاء', style: TextStyle(fontFamily: 'Cairo')),
          onPressed: () => Navigator.pop(context),
        ),
      ),
    );
  }

  Widget _buildActionSheetItem(String title, VoidCallback onPressed) {
    return CupertinoActionSheetAction(
      child: Text(title, style: TextStyle(fontFamily: 'Cairo')),
      onPressed: () {
        Navigator.pop(context);
        onPressed();
      },
    );
  }

  void _handleLogout(BuildContext context) async {
    try {
      await FirebaseAuth.instance.signOut();
      Navigator.of(context).pushAndRemoveUntil(
        CupertinoPageRoute(builder: (context) => LoginPage()),
        (Route<dynamic> route) => false,
      );
    } catch (e) {
      print('Error during logout: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تسجيل الخروج. الرجاء المحاولة مرة أخرى.')),
      );
    }
  }

  Widget _buildExamCard(String title, IconData icon, Widget page) {
    return GestureDetector(
      onTap: () async {
        // التحقق من إمكانية الوصول للصفحة

        if (page is TipsGuidancePage) {  // Removed ImportantMaterialsPage from this check
          if (!_isPremium) {
            Navigator.push(context, CupertinoPageRoute(
              builder: (context) => PremiumSubscriptionPage()
            ));
            return;
          }
        }
        
        // التحقق من محاولات الامتحان
        if (page is EnglishExamPage || page is ArabicExamPage || page is ComputerExamPage) {
          String examType = '';
          if (page is EnglishExamPage) examType = 'english';
          if (page is ArabicExamPage) examType = 'arabic';
          if (page is ComputerExamPage) examType = 'computer';
          
          if (!_isPremium) {
            bool canTake = await SubscriptionService.canTakeExam(examType);
            if (!canTake) {
              Navigator.push(context, CupertinoPageRoute(
                builder: (context) => PremiumSubscriptionPage()
              ));
              return;
            }
            await SubscriptionService.recordExamAttempt(examType);
          }
        }

        Navigator.push(context, CupertinoPageRoute(builder: (context) => page));
      },
      child: Card(
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.white, Colors.grey.shade50],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon, 
                size: 50,
                color: Colors.blue.shade800
              ),
              SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade900,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUpdateQuestionsCard() {
    return GestureDetector(
      onTap: () async {
        if (!_isPremium) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                title: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      'ميزة مقيدة',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[800],
                      ),
                    ),
                    SizedBox(width: 10),
                    Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange[100],
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.workspace_premium,
                        color: Colors.orange[800],
                        size: 24,
                      ),
                    ),
                  ],
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'عزيزي المستخدم،',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.right,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'ميزة تحديث الأسئلة متوفرة حصرياً لمشتركي النسخة المدفوعة. قم بالترقية الآن للحصول على:',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 14,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.right,
                    ),
                    SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          'تحديث مستمر للأسئلة',
                          style: TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: 14,
                            color: Colors.grey[800],
                          ),
                        ),
                        SizedBox(width: 8),
                        Icon(Icons.check_circle, color: Colors.green, size: 20),
                      ],
                    ),
                  ],
                ),
                actionsAlignment: MainAxisAlignment.start,
                actionsPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'لاحقاً',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 14,
                        color: Colors.blue[800],
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      Navigator.push(context, CupertinoPageRoute(
                        builder: (context) => PremiumSubscriptionPage()
                      ));
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[800],
                      padding: EdgeInsets.symmetric(horizontal: 24, vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'ترقية الحساب',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 14,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              );
            },
          );
          return;
        }
        await _updateQuestions();
      },
      child: Card(
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.white, Colors.grey.shade50],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                CupertinoIcons.refresh,
                size: 50,
                color: Colors.blue.shade800
              ),
              SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Text(
                  'تحديث الأسئلة',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade900,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}