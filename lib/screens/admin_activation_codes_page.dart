import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import '../models/activation_code.dart';
import '../services/activation_code_service.dart';

class AdminActivationCodesPage extends StatefulWidget {
  const AdminActivationCodesPage({super.key});

  @override
  _AdminActivationCodesPageState createState() => _AdminActivationCodesPageState();
}

class _AdminActivationCodesPageState extends State<AdminActivationCodesPage> {
  SubscriptionDuration _selectedDuration = SubscriptionDuration.oneMonth;
  List<ActivationCode> _activationCodes = [];
  bool _isLoading = false;
  bool _isGenerating = false;

  @override
  void initState() {
    super.initState();
    _loadActivationCodes();
  }

  Future<void> _loadActivationCodes() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<ActivationCode> codes = await ActivationCodeService.getAllActivationCodes();
      setState(() {
        _activationCodes = codes;
      });
    } catch (e) {
      _showSnackBar('حدث خطأ في تحميل الأكواد: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _generateCode() async {
    setState(() {
      _isGenerating = true;
    });

    try {
      ActivationCode? newCode = await ActivationCodeService.generateActivationCode(_selectedDuration);
      if (newCode != null) {
        setState(() {
          _activationCodes.insert(0, newCode);
        });
        _showSnackBar('تم إنشاء الكود بنجاح: ${newCode.code}');
      } else {
        _showSnackBar('فشل في إنشاء الكود', isError: true);
      }
    } catch (e) {
      _showSnackBar('حدث خطأ في إنشاء الكود: $e', isError: true);
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    _showSnackBar('تم نسخ الكود');
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إدارة أكواد الاشتراك',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        leading: IconButton(
          icon: Icon(CupertinoIcons.back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          _buildGenerateSection(),
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : _buildCodesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerateSection() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'إنشاء كود تفعيل جديد',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          DropdownButtonFormField<SubscriptionDuration>(
            value: _selectedDuration,
            decoration: InputDecoration(
              labelText: 'مدة الاشتراك',
              labelStyle: TextStyle(fontFamily: 'Cairo'),
              border: OutlineInputBorder(),
            ),
            items: SubscriptionDuration.values.map((duration) {
              return DropdownMenuItem(
                value: duration,
                child: Text(
                  duration.displayName,
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedDuration = value;
                });
              }
            },
          ),
          SizedBox(height: 16),
          ElevatedButton(
            onPressed: _isGenerating ? null : _generateCode,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              padding: EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isGenerating
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                      SizedBox(width: 8),
                      Text(
                        'جاري الإنشاء...',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          color: Colors.white,
                        ),
                      ),
                    ],
                  )
                : Text(
                    'إنشاء كود التفعيل',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildCodesList() {
    if (_activationCodes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.doc_text,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد أكواد تفعيل',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: _activationCodes.length,
      itemBuilder: (context, index) {
        return _buildCodeCard(_activationCodes[index]);
      },
    );
  }

  Widget _buildCodeCard(ActivationCode code) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: code.isUsed ? Colors.grey.withOpacity(0.3) : Colors.blue.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  code.code,
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: code.isUsed ? Colors.grey : Colors.black,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => _copyToClipboard(code.code),
                icon: Icon(
                  CupertinoIcons.doc_on_clipboard,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Icon(
                CupertinoIcons.time,
                size: 16,
                color: Colors.grey,
              ),
              SizedBox(width: 4),
              Text(
                'المدة: ${code.duration.displayName}',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          SizedBox(height: 4),
          Row(
            children: [
              Icon(
                code.isUsed ? CupertinoIcons.checkmark_circle_fill : CupertinoIcons.circle,
                size: 16,
                color: code.isUsed ? Colors.green : Colors.orange,
              ),
              SizedBox(width: 4),
              Text(
                code.isUsed ? 'مستخدم' : 'غير مستخدم',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 14,
                  color: code.isUsed ? Colors.green : Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (code.isUsed && code.usedAt != null) ...[
            SizedBox(height: 4),
            Text(
              'تاريخ الاستخدام: ${_formatDate(code.usedAt!)}',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
