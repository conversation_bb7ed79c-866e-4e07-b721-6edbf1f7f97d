import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import '../models/activation_code.dart';
import '../services/activation_code_service.dart';

class AdminActivationCodesPage extends StatefulWidget {
  const AdminActivationCodesPage({super.key});

  @override
  _AdminActivationCodesPageState createState() => _AdminActivationCodesPageState();
}

class _AdminActivationCodesPageState extends State<AdminActivationCodesPage> {
  SubscriptionDuration _selectedDuration = SubscriptionDuration.oneMonth;
  List<ActivationCode> _activationCodes = [];
  List<ActivationCode> _filteredCodes = [];
  bool _isLoading = false;
  bool _isGenerating = false;
  bool _showAllCodes = true;

  @override
  void initState() {
    super.initState();
    _loadActivationCodes();
  }

  Future<void> _loadActivationCodes() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<ActivationCode> codes = await ActivationCodeService.getAllActivationCodes();
      setState(() {
        _activationCodes = codes;
        _filterCodes();
      });
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في تحميل الأكواد: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterCodes() {
    if (_showAllCodes) {
      _filteredCodes = _activationCodes;
    } else {
      _filteredCodes = _activationCodes.where((code) => code.duration == _selectedDuration).toList();
    }
  }



  Future<void> _generateCode() async {
    setState(() {
      _isGenerating = true;
    });

    try {
      ActivationCode? newCode = await ActivationCodeService.generateActivationCode(_selectedDuration);
      if (newCode != null) {
        setState(() {
          _activationCodes.insert(0, newCode);
          _filterCodes();
        });
        _showSuccessSnackBar('تم إنشاء الكود بنجاح: ${newCode.code}');
      } else {
        _showErrorSnackBar('فشل في إنشاء الكود');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في إنشاء الكود: $e');
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  Future<void> _deleteCode(String codeId) async {
    // عرض حوار تأكيد
    bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'تأكيد الحذف',
            style: TextStyle(fontFamily: 'Cairo'),
          ),
          content: Text(
            'هل أنت متأكد من حذف هذا الكود؟ لا يمكن التراجع عن هذا الإجراء.',
            style: TextStyle(fontFamily: 'Cairo'),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'إلغاء',
                style: TextStyle(fontFamily: 'Cairo'),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                'حذف',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  color: Colors.red,
                ),
              ),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      try {
        bool success = await ActivationCodeService.deleteActivationCode(codeId);
        if (success) {
          setState(() {
            _activationCodes.removeWhere((code) => code.id == codeId);
            _filterCodes();
          });
          _showSuccessSnackBar('تم حذف الكود بنجاح');
        } else {
          _showErrorSnackBar('فشل في حذف الكود');
        }
      } catch (e) {
        _showErrorSnackBar('حدث خطأ في حذف الكود: $e');
      }
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    _showSuccessSnackBar('تم نسخ الكود');
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        backgroundColor: Color(0xFF1565C0),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                SizedBox(height: 20),
                _buildDurationSelector(),
                SizedBox(height: 20),
                _buildCodesHeader(),
                SizedBox(height: 12),
                Expanded(
                  child: _isLoading && _activationCodes.isEmpty
                      ? Center(
                          child: CircularProgressIndicator(
                            color: Color(0xFF1565C0),
                          ),
                        )
                      : _filteredCodes.isEmpty
                          ? _buildEmptyState()
                          : _buildCodesList(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(Icons.arrow_back_ios, color: Color(0xFF1565C0)),
        ),
        Expanded(
          child: Text(
            'إدارة أكواد الاشتراك',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
              fontFamily: 'Cairo',
            ),
          ),
        ),
        IconButton(
          onPressed: _loadActivationCodes,
          icon: Icon(Icons.refresh, color: Color(0xFF1565C0)),
          tooltip: 'تحديث القائمة',
        ),
      ],
    );
  }

  Widget _buildCodesHeader() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الأكواد المنشأة (${_filteredCodes.length} من ${_activationCodes.length}):',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black,
                fontFamily: 'Cairo',
              ),
            ),
            PopupMenuButton<String>(
              icon: Icon(Icons.filter_list, color: Color(0xFF1565C0)),
              onSelected: (String value) {
                setState(() {
                  if (value == 'all') {
                    _showAllCodes = true;
                  } else {
                    _showAllCodes = false;
                    _selectedDuration = SubscriptionDurationExtension.fromString(value);
                  }
                  _filterCodes();
                });
              },
              itemBuilder: (BuildContext context) => [
                PopupMenuItem<String>(
                  value: 'all',
                  child: Text('جميع الأكواد', style: TextStyle(fontFamily: 'Cairo')),
                ),
                PopupMenuItem<String>(
                  value: 'one_month',
                  child: Text('شهر واحد', style: TextStyle(fontFamily: 'Cairo')),
                ),
                PopupMenuItem<String>(
                  value: 'three_months',
                  child: Text('3 أشهر', style: TextStyle(fontFamily: 'Cairo')),
                ),
                PopupMenuItem<String>(
                  value: 'six_months',
                  child: Text('6 أشهر', style: TextStyle(fontFamily: 'Cairo')),
                ),
                PopupMenuItem<String>(
                  value: 'one_year',
                  child: Text('سنة واحدة', style: TextStyle(fontFamily: 'Cairo')),
                ),
              ],
            ),
          ],
        ),
        if (!_showAllCodes) ...[
          SizedBox(height: 8),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Color(0xFF1565C0).withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'فلترة: ${_selectedDuration.displayName}',
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFF1565C0),
                fontFamily: 'Cairo',
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            CupertinoIcons.doc_text,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد أكواد منشأة بعد',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDurationSelector() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر مدة الاشتراك:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
              fontFamily: 'Cairo',
            ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDurationOption(SubscriptionDuration.oneMonth),
              ),
              SizedBox(width: 8),
              Expanded(
                child: _buildDurationOption(SubscriptionDuration.threeMonths),
              ),
              SizedBox(width: 8),
              Expanded(
                child: _buildDurationOption(SubscriptionDuration.sixMonths),
              ),
              SizedBox(width: 8),
              Expanded(
                child: _buildDurationOption(SubscriptionDuration.oneYear),
              ),
            ],
          ),
          SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isGenerating ? null : _generateCode,
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF1565C0),
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isGenerating
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Text(
                      'إنشاء كود جديد',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontFamily: 'Cairo',
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDurationOption(SubscriptionDuration duration) {
    final isSelected = _selectedDuration == duration;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDuration = duration;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? Color(0xFF1565C0) : Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Color(0xFF1565C0) : Colors.grey[300]!,
            width: 2,
          ),
        ),
        child: Text(
          duration.displayName,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: isSelected ? Colors.white : Colors.black,
            fontFamily: 'Cairo',
          ),
        ),
      ),
    );
  }



  Widget _buildCodesList() {
    return ListView.builder(
      itemCount: _filteredCodes.length,
      itemBuilder: (context, index) {
        return _buildCodeCard(_filteredCodes[index]);
      },
    );
  }

  Widget _buildCodeCard(ActivationCode code) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      code.code,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1565C0),
                        fontFamily: 'monospace',
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'المدة: ${code.duration.displayName}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: () => _copyToClipboard(code.code),
                    icon: Icon(Icons.copy, color: Color(0xFF1565C0)),
                    tooltip: 'نسخ الكود',
                  ),
                  if (!code.isUsed)
                    IconButton(
                      onPressed: () => _deleteCode(code.id),
                      icon: Icon(Icons.delete, color: Colors.red),
                      tooltip: 'حذف الكود',
                    ),
                ],
              ),
            ],
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Icon(
                code.isUsed ? Icons.check_circle : Icons.radio_button_unchecked,
                color: code.isUsed ? Colors.green : Colors.orange,
                size: 16,
              ),
              SizedBox(width: 4),
              Text(
                code.isUsed ? 'مستخدم' : 'غير مستخدم',
                style: TextStyle(
                  fontSize: 12,
                  color: code.isUsed ? Colors.green : Colors.orange,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                ),
              ),
              Spacer(),
              Text(
                'تاريخ الإنشاء: ${_formatDate(code.createdAt)}',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[500],
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
