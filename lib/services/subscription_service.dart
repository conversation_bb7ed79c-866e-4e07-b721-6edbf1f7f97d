import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class SubscriptionService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  static Future<bool> isPremiumUser() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return false;

      // التحقق من طريقة المصادقة للمستخدم
      final isGoogleSignIn = user.providerData
          .any((element) => element.providerId == 'google.com');

      if (isGoogleSignIn) {
        // للمستخدمين عبر Google، نبحث عن طريق البريد الإلكتروني
        final userDocs = await _firestore.collection('users')
            .where('email', isEqualTo: user.email)
            .limit(1)
            .get();
            
        if (userDocs.docs.isNotEmpty) {
          final userData = userDocs.docs.first.data();
          return userData['isPremium'] ?? false;
        }
        return false;
      } else {
        // للمستخدمين العاديين، نستخدم معرف المستخدم
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        final userData = userDoc.data();
        return userData?['isPremium'] ?? false;
      }
    } catch (e) {
      print('Error checking premium status: $e');
      return false;
    }
  }

  static Future<bool> canTakeExam(String examType) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return false;

      final isPremium = await isPremiumUser();
      if (isPremium) return true;

      // التحقق من طريقة المصادقة للمستخدم
      final isGoogleSignIn = user.providerData
          .any((element) => element.providerId == 'google.com');

      if (isGoogleSignIn) {
        // للمستخدمين عبر Google، نبحث عن طريق البريد الإلكتروني
        final userDocs = await _firestore.collection('users')
            .where('email', isEqualTo: user.email)
            .limit(1)
            .get();
            
        if (userDocs.docs.isNotEmpty) {
          final userData = userDocs.docs.first.data();
          final attempts = userData['examAttempts'] ?? {};
          final hasAttempted = attempts.containsKey(examType) && attempts[examType] == true;
          return !hasAttempted;
        }
        return true; // لم يتم العثور على المستخدم، نسمح بالمحاولة الأولى
      } else {
        // للمستخدمين العاديين، نستخدم معرف المستخدم
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        final userData = userDoc.data();
        final attempts = userData?['examAttempts'] ?? {};
        final hasAttempted = attempts.containsKey(examType) && attempts[examType] == true;
        return !hasAttempted;
      }
    } catch (e) {
      print('Error checking exam attempt: $e');
      return false;
    }
  }

  static Future<void> recordExamAttempt(String examType) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final isPremium = await isPremiumUser();
      if (isPremium) return; // لا نسجل المحاولات للمستخدمين المميزين

      // التحقق من طريقة المصادقة للمستخدم
      final isGoogleSignIn = user.providerData
          .any((element) => element.providerId == 'google.com');

      if (isGoogleSignIn) {
        // للمستخدمين عبر Google، نبحث عن طريق البريد الإلكتروني
        final userDocs = await _firestore.collection('users')
            .where('email', isEqualTo: user.email)
            .limit(1)
            .get();
            
        if (userDocs.docs.isNotEmpty) {
          final docId = userDocs.docs.first.id;
          Map<String, dynamic> currentAttempts = (userDocs.docs.first.data()['examAttempts']) ?? {};
          currentAttempts[examType] = true;

          await _firestore.collection('users').doc(docId).update({
            'examAttempts': currentAttempts,
          });
        }
      } else {
        // للمستخدمين العاديين، نستخدم معرف المستخدم
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        Map<String, dynamic> currentAttempts = (userDoc.data()?['examAttempts']) ?? {};
        currentAttempts[examType] = true;

        await _firestore.collection('users').doc(user.uid).update({
          'examAttempts': currentAttempts,
        });
      }
    } catch (e) {
      print('Error recording exam attempt: $e');
    }
  }

  static Future<void> upgradeToPremium(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).set({
        'isPremium': true,
        'premiumStartDate': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
    } catch (e) {
      print('Error upgrading to premium: $e');
      throw e;
    }
  }
  
  static Future<void> removePremium(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).set({
        'isPremium': false,
      }, SetOptions(merge: true));
    } catch (e) {
      print('Error removing premium status: $e');
      throw e;
    }
  }
}