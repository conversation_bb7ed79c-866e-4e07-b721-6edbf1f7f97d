# نظام أكواد تفعيل الاشتراك المميز

## نظرة عامة

تم تطوير نظام جديد لتفعيل الاشتراك المميز باستخدام أكواد التفعيل بدلاً من الدفع المباشر. يتيح هذا النظام للأدمن إنشاء أكواد تفعيل بفترات مختلفة ومنحها للمستخدمين لتفعيل اشتراكهم المميز.

## المكونات الرئيسية

### 1. نموذج البيانات (`lib/models/activation_code.dart`)
- **ActivationCode**: كلاس يحتوي على جميع بيانات كود التفعيل
- **SubscriptionDuration**: enum للفترات المختلفة (شهر، 3 أشهر، 6 أشهر، سنة)

### 2. خدمة إدارة الأكواد (`lib/services/activation_code_service.dart`)
- **generateActivationCode()**: إنشاء كود تفعيل جديد (للأدمن فقط)
- **activateSubscription()**: تفعيل الاشتراك باستخدام الكود
- **getAllActivationCodes()**: الحصول على جميع الأكواد (للأدمن فقط)

### 3. واجهة الأدمن (`lib/screens/admin_activation_codes_page.dart`)
- إنشاء أكواد تفعيل جديدة
- اختيار مدة الاشتراك
- عرض جميع الأكواد المُنشأة
- نسخ الأكواد للحافظة
- عرض حالة الاستخدام

### 4. واجهة المستخدم (`lib/screens/activate_subscription_page.dart`)
- إدخال كود التفعيل
- تفعيل الاشتراك المميز
- عرض رسائل النجاح والخطأ

### 5. تحديث نظام الاشتراك (`lib/services/subscription_service.dart`)
- دعم تواريخ انتهاء الاشتراك
- التحقق التلقائي من انتهاء الصلاحية
- تحديث حالة الاشتراك المنتهي

## كيفية الاستخدام

### للأدمن:
1. الدخول للصفحة الرئيسية
2. النقر على بطاقة "إدارة أكواد الاشتراك" (تظهر للأدمن فقط)
3. اختيار مدة الاشتراك المطلوبة
4. النقر على "إنشاء كود التفعيل"
5. نسخ الكود ومشاركته مع المستخدم

### للمستخدم:
1. الدخول للصفحة الرئيسية
2. النقر على أيقونة المستخدم في الأعلى
3. اختيار "تفعيل الاشتراك المميز" (يظهر للمستخدمين غير المميزين فقط)
4. إدخال كود التفعيل
5. النقر على "تفعيل الاشتراك"

## الفترات المتاحة

- **شهر واحد**: 30 يوم
- **3 أشهر**: 90 يوم
- **6 أشهر**: 180 يوم
- **سنة واحدة**: 365 يوم

## قاعدة البيانات

### مجموعة `activation_codes`
```json
{
  "code": "ABC12345",
  "duration": "three_months",
  "isUsed": false,
  "usedByUserId": null,
  "usedAt": null,
  "createdAt": "2024-01-01T00:00:00Z",
  "createdByAdminId": "admin_uid"
}
```

### تحديث مجموعة `users`
```json
{
  "isPremium": true,
  "premiumStartDate": "2024-01-01T00:00:00Z",
  "premiumExpiryDate": "2024-04-01T00:00:00Z"
}
```

## الأمان

- فقط الأدمن يمكنه إنشاء أكواد التفعيل
- كل كود يُستخدم مرة واحدة فقط
- التحقق التلقائي من انتهاء صلاحية الاشتراك
- تشفير الأكواد وعشوائيتها

## الاختبارات

تم إنشاء ملف اختبار شامل في `test/activation_code_test.dart` يغطي:
- إنشاء كود التفعيل
- تحويل البيانات من وإلى Map
- اختبار فترات الاشتراك
- اختبار نسخ الكود مع التحديثات

## التشغيل والاختبار

```bash
# تشغيل الاختبارات
flutter test

# تشغيل التطبيق
flutter run
```

## ملاحظات مهمة

1. تأكد من أن معرف الأدمن محدث في الكود
2. الأكواد تُنشأ بشكل عشوائي وفريد
3. يتم التحقق من انتهاء الصلاحية عند كل تسجيل دخول
4. النظام يدعم تسجيل الدخول عبر Google والبريد الإلكتروني

## المزايا الجديدة

- **مرونة في الإدارة**: الأدمن يتحكم في إنشاء الأكواد
- **أمان عالي**: كل كود يُستخدم مرة واحدة
- **سهولة الاستخدام**: واجهة بسيطة للمستخدمين
- **تتبع شامل**: معرفة من استخدم الكود ومتى
- **انتهاء تلقائي**: النظام يتعامل مع انتهاء الصلاحية تلقائياً
